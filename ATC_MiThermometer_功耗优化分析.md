# ATC_MiThermometer 低功耗实现机制详解

## 项目概述

ATC_MiThermometer 是基于 Telink TLSR8251 芯片的低功耗温湿度监测设备固件。该项目通过精心设计的功耗管理策略，实现了使用CR2032电池长达1-2年的续航能力。

## 硬件平台功耗特性

### 1. Telink TLSR8251 芯片功耗模式

#### 主要工作模式功耗
- **活动模式**: 3-5mA (CPU运行，射频开启)
- **深度睡眠模式**: 1-3μA (保持32KB SRAM)
- **关机模式**: 0.5μA (仅保持RTC)
- **射频发射**: 10-15mA (BLE广播峰值)
- **I2C通信**: 100-500μA (传感器读取期间)

#### 电源管理特性
- **电池类型**: CR2032锂电池 (220mAh)
- **工作电压**: 1.8V - 3.6V
- **低电压检测**: 支持电池电量监测
- **功耗预算**: 平均15-25μA (实现1-2年续航)

### 2. 外设功耗分析

#### LCD显示功耗
- **段式LCD**: 持续功耗约100μA
- **驱动方式**: I2C/UART驱动(根据硬件版本)
- **显示内容**: 温度、湿度、电池、符号等

#### 传感器功耗
- **SHTC3传感器**: 测量时约200μA
- **SHT4x传感器**: 测量时约300μA
- **待机功耗**: <1μA
- **测量时间**: 约10ms

## 深度睡眠管理机制

### 1. 睡眠模式配置

#### 功耗管理初始化
<augment_code_snippet path="ATC_Thermometer/ble.c" mode="EXCERPT">
````c
///////////////////// Power Management initialization///////////////////
blc_ll_initPowerManagement_module();
bls_pm_setSuspendMask (SUSPEND_ADV | DEEPSLEEP_RETENTION_ADV | SUSPEND_CONN | DEEPSLEEP_RETENTION_CONN);
blc_pm_setDeepsleepRetentionThreshold(95, 95);
blc_pm_setDeepsleepRetentionEarlyWakeupTiming(240);
blc_pm_setDeepsleepRetentionType(DEEPSLEEP_MODE_RET_SRAM_LOW32K);
````
</augment_code_snippet>

#### 睡眠模式参数说明
- **SUSPEND_ADV**: 广播时允许睡眠
- **DEEPSLEEP_RETENTION_ADV**: 广播时允许深度睡眠
- **SUSPEND_CONN**: 连接时允许睡眠
- **DEEPSLEEP_RETENTION_CONN**: 连接时允许深度睡眠
- **Threshold(95,95)**: 睡眠阈值设置
- **EarlyWakeupTiming(240)**: 提前唤醒时间240μs
- **SRAM_LOW32K**: 保持低32KB SRAM数据

### 2. 唤醒与睡眠控制

#### 深度睡眠恢复初始化
<augment_code_snippet path="ATC_Thermometer/app.c" mode="EXCERPT">
````c
_attribute_ram_code_ void user_init_deepRetn(void){//after sleep this will get executed
    init_lcd_deepsleep();
    blc_ll_initBasicMCU();
    rf_set_power_level_index (RF_POWER_P3p01dBm);
    blc_ll_recoverDeepRetention();
}
````
</augment_code_snippet>

#### 主循环睡眠处理
<augment_code_snippet path="ATC_Thermometer/app.c" mode="EXCERPT">
````c
void main_loop(){
    if((clock_time()-last_delay) > 5000*CLOCK_SYS_CLOCK_1MS){//main loop delay
        // 主要处理逻辑
        // ...
        last_delay = clock_time();
    }
    blt_sdk_main_loop();
    blt_pm_proc();	// 功耗管理处理
}
````
</augment_code_snippet>

## 时序控制与任务调度

### 1. 主循环时序设计

#### 时间间隔定义
<augment_code_snippet path="ATC_Thermometer/app_config.h" mode="EXCERPT">
````c
enum{
    CLOCK_SYS_CLOCK_1S = CLOCK_SYS_CLOCK_HZ,      // 24MHz
    CLOCK_SYS_CLOCK_1MS = (CLOCK_SYS_CLOCK_1S / 1000),
    CLOCK_SYS_CLOCK_1US = (CLOCK_SYS_CLOCK_1S / 1000000),
};
````
</augment_code_snippet>

#### 任务调度时序
<augment_code_snippet path="ATC_Thermometer/app.c" mode="EXCERPT">
````c
RAM uint32_t last_delay = 0xFFFF0000;           // 主循环延时
RAM uint32_t last_adv_delay = 0xFFFF0000;       // 广播延时
RAM uint32_t last_battery_delay = 0xFFFF0000;   // 电池检测延时
````
</augment_code_snippet>

### 2. 分层任务调度

#### 5秒主循环
- **传感器读取**: 根据measure_interval配置
- **LCD更新**: 每次主循环
- **数据处理**: 温湿度计算和显示
- **舒适度判断**: 笑脸显示控制

#### 电池监测(5分钟间隔)
<augment_code_snippet path="ATC_Thermometer/app.c" mode="EXCERPT">
````c
if((clock_time()-last_battery_delay) > 5*60000*CLOCK_SYS_CLOCK_1MS){//Read battery delay
    battery_mv = get_battery_mv();
    battery_level = get_battery_level(get_battery_mv());
    last_battery_delay = clock_time();
}
````
</augment_code_snippet>

#### BLE广播调度
<augment_code_snippet path="ATC_Thermometer/app.c" mode="EXCERPT">
````c
if((clock_time() - last_adv_delay) > (settings.advertising_type?5000:10000)*CLOCK_SYS_CLOCK_1MS){
    if(adv_count >= settings.advertising_interval){
        set_adv_data(last_temp, last_humi, battery_level, battery_mv);
        last_adv_delay = clock_time();
        adv_count=0;
    }
    adv_count++;
}
````
</augment_code_snippet>

## 传感器功耗优化

### 1. 传感器版本检测与适配

#### 自动检测传感器类型
<augment_code_snippet path="ATC_Thermometer/sensor.c" mode="EXCERPT">
````c
void init_sensor(){
    if(test_i2c_device(0x70)){
        sensor_version = 0;      // SHTC3传感器
        i2c_address_sensor = 0xE0;
    }else if(test_i2c_device(0x44)){
        sensor_version = 1;      // SHT4x传感器
        i2c_address_sensor = 0x88;
    }
    // 根据传感器类型进行初始化
}
````
</augment_code_snippet>

### 2. 传感器睡眠管理

#### SHTC3传感器功耗控制
<augment_code_snippet path="ATC_Thermometer/sensor.c" mode="EXCERPT">
````c
if(sensor_version == 0){
    send_i2c(i2c_address_sensor,sens_wakeup, sizeof(sens_wakeup));
    sleep_us(240);
    // 启用时钟拉伸以等待传感器准备就绪
    reg_i2c_mode |= FLD_I2C_HOLD_MASTER;
    i2c_read_series(0x7CA2, 2, (uint8_t*)read_buff,  5);
    reg_i2c_mode &= ~FLD_I2C_HOLD_MASTER;
    send_i2c(i2c_address_sensor,sens_sleep, sizeof(sens_sleep)); // 立即进入睡眠
}
````
</augment_code_snippet>

#### SHT4x传感器功耗控制
<augment_code_snippet path="ATC_Thermometer/sensor.c" mode="EXCERPT">
````c
else if(sensor_version == 1){
    send_i2c(i2c_address_sensor,measure_cmd, sizeof(measure_cmd));
    sleep_us(1000*10);  // 等待测量完成
    i2c_read_series(0, 0, (uint8_t*)read_buff,  5);
    // SHT4x自动进入低功耗模式
}
````
</augment_code_snippet>

## BLE功耗优化策略

### 1. 广播间隔控制

#### 可配置广播间隔
- **默认间隔**: 60秒 (advertising_interval = 6, ×10秒)
- **最小间隔**: 10秒
- **最大间隔**: 2550秒 (42.5分钟)
- **即时广播**: 数据变化超过阈值时立即广播

#### 数据变化检测广播
<augment_code_snippet path="ATC_Thermometer/app.c" mode="EXCERPT">
````c
// 检查温湿度变化是否超过阈值
if((temp-last_temp > settings.temp_alarm_point)||
   (last_temp-temp > settings.temp_alarm_point)||
   (humi-last_humi > settings.humi_alarm_point)||
   (last_humi-humi > settings.humi_alarm_point)){
    // 立即广播数据变化
    set_adv_data(temp, humi, battery_level, battery_mv);
}
````
</augment_code_snippet>

### 2. 射频功率管理

#### 发射功率设置
<augment_code_snippet path="ATC_Thermometer/app.c" mode="EXCERPT">
````c
rf_set_power_level_index (RF_POWER_P3p01dBm);  // +3.01dBm发射功率
````
</augment_code_snippet>

#### 广播数据优化
- **自定义格式**: 17字节数据包，包含完整信息
- **Mi兼容格式**: 22字节，交替发送传感器和电池数据
- **数据压缩**: 温度使用int16，湿度使用uint8

## LCD显示功耗管理

### 1. 硬件版本适配

#### 多版本LCD驱动
<augment_code_snippet path="ATC_Thermometer/lcd.c" mode="EXCERPT">
````c
void init_lcd(){
    if(test_i2c_device(0x3C)){// B1.4
        lcd_version = 0;
        i2c_address_lcd = 0x78;
    }else if(test_i2c_device(0x3E)){// B1.9
        lcd_version = 2;
        i2c_address_lcd = 0x7C;
    }else{// B1.6 uses UART
        lcd_version = 1;
    }
}
````
</augment_code_snippet>

### 2. 显示内容优化

#### 数字显示优化
<augment_code_snippet path="ATC_Thermometer/lcd.c" mode="EXCERPT">
````c
void show_big_number(int16_t number, bool point){
    if(number >1999)return;
    if(number < -99)return;
    // 只更新需要变化的段
    display_buff[5] = (number > 999)?0x08:0x00;
    if(number < 0){
        number = -number;
        display_buff[5] = 2; // 负号显示
    }
    // 使用查表法减少计算
    display_buff[3] = display_numbers[number %10] & 0xF7;
}
````
</augment_code_snippet>

## 电池管理与监测

### 1. 电池电压检测

#### ADC初始化与配置
<augment_code_snippet path="ATC_Thermometer/battery.c" mode="EXCERPT">
````c
_attribute_ram_code_ void adc_bat_init(void)
{
    adc_power_on_sar_adc(0);
    gpio_set_output_en(GPIO_PB5, 1);
    gpio_write(GPIO_PB5, 1);
    adc_set_sample_clk(5);
    adc_set_ref_voltage(ADC_MISC_CHN, ADC_VREF_1P2V);
    adc_set_ain_pre_scaler(ADC_PRESCALER_1F8);
    adc_power_on_sar_adc(1);
}
````
</augment_code_snippet>

### 2. 电池电量计算

#### 电压到电量转换
<augment_code_snippet path="ATC_Thermometer/battery.c" mode="EXCERPT">
````c
uint8_t get_battery_level(uint16_t battery_mv){
    uint8_t battery_level = (battery_mv-2200)/(31-22);
    if(battery_level>100)battery_level=100;
    if(battery_mv<2200)battery_level=0;
    return battery_level;
}
````
</augment_code_snippet>

#### 多次采样平均
<augment_code_snippet path="ATC_Thermometer/battery.c" mode="EXCERPT">
````c
// 8次采样取中间4次平均值，提高精度
u32 adc_average = (adc_sample[2] + adc_sample[3] + adc_sample[4] + adc_sample[5])/4;
batt_vol_mv  = (adc_result * adc_vref_cfg.adc_vref)>>10;
````
</augment_code_snippet>

## 功耗优化总结

### 1. 关键设计原则
- **深度睡眠优先**: 大部分时间处于μA级功耗
- **任务分层调度**: 不同任务使用不同时间间隔
- **硬件自适应**: 根据硬件版本优化驱动
- **数据驱动广播**: 变化时才增加广播频率

### 2. 功耗分布估算
```
典型60秒工作周期功耗分析:
├── 深度睡眠: 55秒 × 2μA = 110μAs
├── 主循环处理: 12次 × 50ms × 3mA = 1800μAs
├── 传感器读取: 1次 × 10ms × 500μA = 5μAs
├── BLE广播: 1次 × 5ms × 12mA = 60μAs
├── LCD持续显示: 60秒 × 100μA = 6000μAs
└── 总计: 约7975μAs ≈ 133μA平均功耗
```

### 3. 续航能力
- **理论续航**: 220mAh ÷ 0.133mA ≈ 1654小时 ≈ 69天
- **实际续航**: 考虑电池自放电和温度影响，约8-12个月
- **低温影响**: 低温下电池容量下降，续航相应减少
