# ATC_MiThermometer 历史数据存储机制详解

## 项目概述

ATC_MiThermometer 是小米温湿度计 LYWSD03MMC 的自定义固件项目，基于 Telink TLSR8251 芯片开发。该项目提供了温湿度监测、BLE广播、LCD显示等功能。

## Flash存储架构分析

### 1. Flash存储区域划分

#### 当前存储布局
```
Flash总容量: 512KB
├── 0x00000 - 0x3FFFF: 固件代码区域 (256KB)
├── 0x40000 - 0x77FFF: OTA备份区域 (224KB)
├── 0x78000 - 0x780FF: Mi原厂数据擦除区 (256字节)
├── 0x78100 - 0x781FF: 用户设置存储区 (256字节)
└── 0x78200 - 0x7FFFF: 未使用区域 (约30KB)
```

#### Mi数据擦除机制
<augment_code_snippet path="ATC_Thermometer/flash.c" mode="EXCERPT">
````c
void erase_mi_data(){
    uint8_t read[0x100];//ERASE THE MI ID to prevent blocking :D
    memset(read,0x00,0x100);
    flash_read_page(0x78000, 0x100, read);
    if((read[0] != 0xff) && (read[1] != 0xff) && (read[2] != 0xff) && (read[3] != 0xff) && (read[4] != 0xff))
        flash_erase_sector(0x78000);
}
````
</augment_code_snippet>

### 2. 设置数据存储结构

#### 设置结构体定义
<augment_code_snippet path="ATC_Thermometer/flash.h" mode="EXCERPT">
````c
struct Settings_struct
{
    uint32_t magic;                    // 魔数验证 (0xABCFF123)
    uint32_t len;                      // 结构体长度
    uint8_t temp_C_or_F;              // 温度显示单位
    uint8_t advertising_temp_C_or_F;   // 广播温度单位
    uint8_t blinking_smiley;          // 笑脸闪烁设置
    uint8_t comfort_smiley;           // 舒适度指示器
    uint8_t show_batt_enabled;        // 电池显示开关
    uint8_t advertising_type;         // 广播类型(自定义/Mi兼容)
    uint8_t advertising_interval;     // 广播间隔(×10秒)
    uint8_t measure_interval;         // 测量间隔
    int8_t temp_offset;               // 温度偏移(-12.8~+12.8°C)
    int8_t humi_offset;               // 湿度偏移(-50~+50%)
    uint8_t temp_alarm_point;         // 温度报警阈值(÷10)
    uint8_t humi_alarm_point;         // 湿度报警阈值
    uint8_t crc;                      // CRC校验(必须在最后)
};
````
</augment_code_snippet>

#### 设置数据初始化与验证
<augment_code_snippet path="ATC_Thermometer/flash.c" mode="EXCERPT">
````c
void init_flash(){
    erase_mi_data();

    flash_read_page(0x78100,sizeof(settings),(uint8_t*)&settings);

    if((settings.magic != MAGIC_WORD) | (settings.crc != get_crc()) | (settings.len != sizeof(settings)))
    {
        reset_settings_to_default();
        save_settings_to_flash();
    }
}
````
</augment_code_snippet>

## 数据处理与存储流程

### 1. 实时数据管理

#### RAM数据缓存
<augment_code_snippet path="ATC_Thermometer/app.c" mode="EXCERPT">
````c
int16_t temp = 0;                    // 当前温度
uint16_t humi = 0;                   // 当前湿度
RAM int16_t last_temp;               // 上次温度
RAM uint16_t last_humi;              // 上次湿度
RAM uint8_t battery_level;           // 电池电量百分比
RAM uint16_t battery_mv;             // 电池电压(mV)
````
</augment_code_snippet>

#### 传感器数据读取时序
<augment_code_snippet path="ATC_Thermometer/app.c" mode="EXCERPT">
````c
if(meas_count >= settings.measure_interval){
    read_sensor(&temp,&humi);
    temp += settings.temp_offset;
    humi += settings.humi_offset;
    meas_count=0;

    // 检查数据变化是否超过阈值，决定是否立即广播
    if((temp-last_temp > settings.temp_alarm_point)||
       (last_temp-temp > settings.temp_alarm_point)||
       (humi-last_humi > settings.humi_alarm_point)||
       (last_humi-humi > settings.humi_alarm_point)){
        // 立即广播数据变化
        set_adv_data(temp, humi, battery_level, battery_mv);
    }
    last_temp = temp;
    last_humi = humi;
}
````
</augment_code_snippet>

### 2. 数据广播机制

#### BLE广播数据格式
<augment_code_snippet path="ATC_Thermometer/ble.c" mode="EXCERPT">
````c
// 自定义广播格式
RAM uint8_t	advertising_data[] = {
 /*Description*/16, 0x16, 0x1a, 0x18,
 /*MAC*/0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
 /*Temp*/0xaa, 0xaa,
 /*Humi*/0xbb,
 /*BatL*/0xcc,
 /*BatM*/0xdd, 0xdd,
 /*Counter*/0x00
};

// Mi兼容广播格式
RAM uint8_t	advertising_data_Mi[] = {
 /*Description*/21, 0x16, 0x95, 0xfe,
 /*Start*/0x50, 0x30,
 /*Device id*/0x5B, 0x05,
 /*counter*/0x00,
 /*MAC*/0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
 /*Temp+Humi*//*BatL alternating*/0x0D, 0x10, 0x04, 0x00, 0x00, 0x00, 0x00,
};
````
</augment_code_snippet>

#### 广播数据更新逻辑
<augment_code_snippet path="ATC_Thermometer/ble.c" mode="EXCERPT">
````c
void set_adv_data(int16_t temp, uint16_t humi, uint8_t battery_level, uint16_t battery_mv){
    if(settings.advertising_type){//Mi Like Advertising
        // Mi兼容格式：交替发送传感器数据和电池数据
        if(show_temp_humi_Mi){
            advertising_data_Mi[18] = temp&0xff;
            advertising_data_Mi[19] = temp>>8;
            advertising_data_Mi[20] = humi&0xff;
            advertising_data_Mi[21] = humi>>8;
        }else{
            advertising_data_Mi[18] = battery_level;
            // 其他字节清零
        }
        show_temp_humi_Mi = !show_temp_humi_Mi;
    }else{//Custom advertising type
        // 自定义格式：一次发送所有数据
        advertising_data[10] = temp>>8;
        advertising_data[11] = temp&0xff;
        advertising_data[12] = humi&0xff;
        advertising_data[13] = battery_level;
        advertising_data[14] = battery_mv>>8;
        advertising_data[15] = battery_mv&0xff;
        advertising_data[16]++; // 计数器递增
    }
}
````
</augment_code_snippet>

## 当前存储机制的特点与限制

### 1. 存储特点
- **非易失性设置**: 用户配置参数永久保存在Flash中
- **易失性数据**: 传感器数据仅保存在RAM中，断电丢失
- **数据验证**: 使用魔数和CRC校验确保设置数据完整性
- **默认恢复**: 设置损坏时自动恢复默认值

### 2. 当前限制
- **无历史记录**: 只保存当前和上一次的测量值
- **数据丢失**: 断电后历史数据完全丢失
- **存储空间浪费**: 约30KB Flash空间未使用
- **无趋势分析**: 无法提供数据变化趋势

### 3. 数据流向总结
```
传感器 → RAM缓存 → BLE广播 → 外部设备
   ↓
LCD显示
   ↓
(历史数据丢失)
```
