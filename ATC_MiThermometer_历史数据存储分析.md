# ATC_MiThermometer 历史数据存储机制分析

## 项目概述

ATC_MiThermometer 是小米温湿度计 LYWSD03MMC 的自定义固件项目，基于 Telink TLSR8251 芯片开发。该项目提供了温湿度监测、BLE广播、LCD显示等功能。

## 当前数据存储架构

### 1. Flash存储结构

#### 存储区域划分
- **设置存储区域**: `0x78100` - 存储用户配置参数
- **Mi数据擦除区域**: `0x78000` - 用于擦除原厂Mi数据，防止冲突
- **OTA固件区域**: 双bank更新机制，单个固件最大256KB

#### 设置数据结构 (`settings_struct`)
```c
struct Settings_struct {
    uint32_t magic;                    // 魔数验证 (0xABCFF123)
    uint32_t len;                      // 结构体长度
    uint8_t temp_C_or_F;              // 温度显示单位
    uint8_t advertising_temp_C_or_F;   // 广播温度单位
    uint8_t blinking_smiley;          // 笑脸闪烁设置
    uint8_t comfort_smiley;           // 舒适度指示器
    uint8_t show_batt_enabled;        // 电池显示开关
    uint8_t advertising_type;         // 广播类型(自定义/Mi兼容)
    uint8_t advertising_interval;     // 广播间隔(×10秒)
    uint8_t measure_interval;         // 测量间隔
    int8_t temp_offset;               // 温度偏移(-12.8~+12.8°C)
    int8_t humi_offset;               // 湿度偏移(-50~+50%)
    uint8_t temp_alarm_point;         // 温度报警阈值(÷10)
    uint8_t humi_alarm_point;         // 湿度报警阈值
    uint8_t crc;                      // CRC校验(必须在最后)
};
```

### 2. 当前数据处理流程

#### 实时数据处理
1. **传感器读取**: 每5秒主循环中检查是否需要读取传感器
2. **数据缓存**: 使用RAM变量存储当前值
   - `temp` - 当前温度
   - `humi` - 当前湿度  
   - `last_temp` - 上次温度
   - `last_humi` - 上次湿度
   - `battery_level` - 电池电量百分比
   - `battery_mv` - 电池电压(mV)

#### 数据广播机制
1. **定时广播**: 根据`advertising_interval`设置定期广播
2. **即时广播**: 当温湿度变化超过阈值时立即广播
3. **广播格式**: 
   - 自定义格式: 包含MAC、温度、湿度、电池信息
   - Mi兼容格式: 交替发送传感器数据和电池数据

## 历史数据存储的缺失与需求

### 当前限制
1. **无历史记录**: 只保存当前和上一次的测量值
2. **数据丢失**: 断电后历史数据完全丢失
3. **存储空间未利用**: Flash空间大部分未使用
4. **无趋势分析**: 无法提供数据趋势和统计信息

### 历史数据存储需求分析

#### 存储容量估算
- **可用Flash空间**: 约200KB (256KB总容量 - 固件占用)
- **单条记录大小**: 8字节 (时间戳4字节 + 温度2字节 + 湿度1字节 + 电池1字节)
- **理论存储容量**: 约25,000条记录
- **实际建议容量**: 20,000条记录 (预留空间)

#### 数据记录策略
1. **记录间隔**: 可配置(默认10分钟)
2. **循环存储**: 满后覆盖最旧数据
3. **数据压缩**: 使用差分编码减少存储空间
4. **关键事件**: 温湿度突变时额外记录

## 建议的历史数据存储方案

### 1. Flash分区重新规划
```
0x78000 - 0x78100: Mi数据擦除区域 (256字节)
0x78100 - 0x78200: 设置存储区域 (256字节)  
0x78200 - 0x7F000: 历史数据存储区域 (约28KB)
0x7F000 - 0x80000: 预留区域 (4KB)
```

### 2. 历史数据结构设计
```c
typedef struct {
    uint32_t timestamp;     // Unix时间戳
    int16_t temperature;    // 温度 (×10)
    uint8_t humidity;       // 湿度百分比
    uint8_t battery;        // 电池电量百分比
} history_record_t;

typedef struct {
    uint32_t magic;         // 魔数
    uint16_t total_records; // 总记录数
    uint16_t current_index; // 当前写入索引
    uint32_t oldest_timestamp; // 最旧记录时间戳
    uint32_t newest_timestamp; // 最新记录时间戳
} history_header_t;
```

### 3. 数据管理功能
1. **写入管理**: 循环写入，自动覆盖旧数据
2. **读取接口**: 支持按时间范围查询
3. **数据导出**: 通过BLE连接导出历史数据
4. **统计功能**: 提供最大值、最小值、平均值等统计信息

### 4. 功耗优化考虑
1. **批量写入**: 累积多条记录后一次性写入Flash
2. **写入缓存**: 使用RAM缓存减少Flash写入次数
3. **智能记录**: 数据变化不大时延长记录间隔
4. **低功耗模式**: 在深度睡眠时暂停历史记录

## 实现优先级建议

### 第一阶段 (基础功能)
1. Flash分区重新规划
2. 基础历史数据结构实现
3. 简单的循环存储机制

### 第二阶段 (增强功能)  
1. 数据压缩和优化
2. BLE数据导出接口
3. 统计功能实现

### 第三阶段 (高级功能)
1. 智能记录策略
2. 数据分析和趋势预测
3. 与上位机软件集成

## 技术挑战与解决方案

### 1. Flash写入寿命
- **问题**: Flash写入次数有限(约10万次)
- **解决**: 磨损均衡算法，减少写入频率

### 2. 时间戳管理
- **问题**: 设备无RTC，时间戳获取困难
- **解决**: 通过BLE连接同步时间，使用相对时间戳

### 3. 数据一致性
- **问题**: 写入过程中断电可能导致数据损坏
- **解决**: 双缓冲机制，写入完成标志

### 4. 功耗影响
- **问题**: 频繁Flash操作增加功耗
- **解决**: 智能缓存策略，批量操作
